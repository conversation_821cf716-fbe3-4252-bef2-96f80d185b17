# SemiPRO Semiconductor Simulator - Git Ignore File

# Build directories
build/
Build/
BUILD/
out/
dist/
bin/
lib/
libs/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
CTestTestfile.cmake
Makefile
*.cmake
!CMakeLists.txt

# Compiled binaries
*.exe
*.out
*.app
*.dll
*.so
*.dylib
*.a
*.lib

# Object files
*.o
*.obj
*.lo
*.slo
*.ko

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Temporary files
*.tmp
*.temp
*~
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.vcxproj*
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
*.code-workspace

# Visual Studio Code
.vscode/
*.code-workspace

# CLion
.idea/
cmake-build-*/

# Qt Creator
CMakeLists.txt.user*

# Xcode
*.xcodeproj/
*.xcworkspace/

# Logs and runtime files
logs/
*.log
*.log.*
core.*
!src/cpp/core/
vgcore.*

# Configuration files (keep templates)
config/*.yaml
config/*.yml
config/*.json
!config/*.template.*
!config/example_*

# Python cache and bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# Python distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Vulkan and graphics
*.spv
shaders/compiled/
vulkan_debug.txt

# Documentation build
docs/_build/
docs/build/
site/

# Test results and coverage
*.gcov
*.gcno
*.gcda
coverage/
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Profiling data
*.prof
*.perf
callgrind.out.*
massif.out.*
gmon.out

# Memory debugging
valgrind-*.log
*.memcheck

# Backup files
*.bak
*.backup
*.orig

# Archive files
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.rar
*.7z

# Package manager files
Pipfile.lock
poetry.lock
yarn.lock
package-lock.json

# Environment variables
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Simulation output files
results/
output/
data/
*.dat
*.csv
*.hdf5
*.h5

# Large data files
*.bin
*.raw
*.dump

# Temporary simulation files
sim_temp_*
wafer_*.tmp
process_*.cache

# But keep source code
!src/
!src/**/*

# Generated documentation
doxygen/
sphinx/_build/

# Conan package manager
conanfile.txt
conaninfo.txt
conanbuildinfo.*
conan.lock

# vcpkg
vcpkg_installed/

# FetchContent
_deps/

# CCache
.ccache/

# Ninja
.ninja_deps
.ninja_log

# Clang
.clangd/
compile_commands.json

# Static analysis
cppcheck-*.xml
scan-build-*/

# Sanitizer outputs
*.asan
*.tsan
*.msan
*.ubsan

# Benchmark results
benchmark_results/
*.benchmark

# Machine learning models (if any)
*.model
*.pkl
*.joblib

# Simulation checkpoints
checkpoints/
*.checkpoint

# Video and image outputs
*.mp4
*.avi
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# CAD files (large)
*.gds
*.gdsii
*.oasis

# SPICE simulation files
*.sp
*.cir
*.lis
*.out

# Custom application data
.semipro/
user_data/
preferences.ini

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk
*.url

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# macOS
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

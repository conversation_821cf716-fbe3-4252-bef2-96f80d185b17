Welcome to Semiconductor Simulator's Documentation
==================================================

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   api/index
   tutorials/index
   examples/index

The Semiconductor Simulator is a modular framework for simulating semiconductor fabrication processes, including:

- **Geometry**: Wafer grid initialization
- **Oxidation**: Thermal oxide growth
- **Doping**: Ion implantation and diffusion
- **Photolithography**: Pattern exposure
- **Deposition**: Film deposition (CVD, PVD)
- **Etching**: Material removal
- **Metallization**: Metal interconnects
- **Packaging**: Die bonding and electrical testing
- **Thermal Simulation**: Heat distribution modeling
- **Reliability Testing**: Electromigration, thermal stress, dielectric breakdown
   

   api/
   tutorials/
   ├── geometry.rst
   ├── oxidation.rst
   ├── doping.rst
   ├── photolithography.rst
   ├── deposition.rst
   ├── etching.rst
   ├── metallization.rst
   ├── packaging.rst
   ├── thermal.rst
   examples/
   ├── geometry.rst
   ├── oxidation.rst
   ├── doping.rst
   ├── photolithography.rst
   ├── deposition.rst
   ├── etching.rst
   ├── metallization.rst
   ├── packaging.rst
   ├── thermal.rst
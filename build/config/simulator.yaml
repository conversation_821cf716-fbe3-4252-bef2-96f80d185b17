simulator:
  version: "1.0.0"
  
wafer:
  default_diameter: 300.0  # mm
  default_thickness: 775.0  # um
  default_material: "silicon"
  
grid:
  default_resolution: 100
  max_resolution: 1000
  
modules:
  geometry:
    enabled: true
  oxidation:
    enabled: true
    default_temperature: 1000.0  # C
    default_time: 60.0  # minutes
  doping:
    enabled: true
    monte_carlo_particles: 10000
  photolithography:
    enabled: true
    default_wavelength: 13.5  # nm (EUV)
  deposition:
    enabled: true
    default_rate: 0.1  # um/min
  etching:
    enabled: true
    default_rate: 0.05  # um/min
  metallization:
    enabled: true
    default_metals: ["Cu", "Al", "W"]
  packaging:
    enabled: true
  thermal:
    enabled: true
    ambient_temperature: 300.0  # K
  reliability:
    enabled: true
    
rendering:
  vulkan:
    enabled: true
    validation_layers: false
  window:
    width: 1024
    height: 768
    title: "SemiPRO Semiconductor Simulator"
    
performance:
  parallel_processing: true
  max_threads: 8
  memory_limit: "4GB"

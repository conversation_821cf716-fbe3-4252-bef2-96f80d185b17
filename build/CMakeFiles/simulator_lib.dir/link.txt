/usr/bin/ar qc libsimulator_lib.a CMakeFiles/simulator_lib.dir/src/cpp/core/wafer.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/core/utils.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/geometry/geometry_manager.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/oxidation/oxidation_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/doping/doping_manager.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/doping/monte_carlo_solver.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/doping/diffusion_solver.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/photolithography/lithography_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/deposition/deposition_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/etching/etching_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/metallization/metallization_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/packaging/packaging_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/thermal/thermal_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/modules/reliability/reliability_model.cpp.o CMakeFiles/simulator_lib.dir/src/cpp/renderer/vulkan_renderer.cpp.o
/usr/bin/ranlib libsimulator_lib.a

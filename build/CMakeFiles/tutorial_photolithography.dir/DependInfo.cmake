
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Documents/dev/projects/SemiPRO/tutorials/cpp/tutorial_photolithography.cpp" "CMakeFiles/tutorial_photolithography.dir/tutorials/cpp/tutorial_photolithography.cpp.o" "gcc" "CMakeFiles/tutorial_photolithography.dir/tutorials/cpp/tutorial_photolithography.cpp.o.d"
  "" "tutorial_photolithography" "gcc" "CMakeFiles/tutorial_photolithography.dir/link.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")

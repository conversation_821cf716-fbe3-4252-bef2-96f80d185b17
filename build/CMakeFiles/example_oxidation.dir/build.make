# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/dev/projects/SemiPRO

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/dev/projects/SemiPRO/build

# Include any dependencies generated for this target.
include CMakeFiles/example_oxidation.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/example_oxidation.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/example_oxidation.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/example_oxidation.dir/flags.make

CMakeFiles/example_oxidation.dir/codegen:
.PHONY : CMakeFiles/example_oxidation.dir/codegen

CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o: CMakeFiles/example_oxidation.dir/flags.make
CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o: /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_oxidation.cpp
CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o: CMakeFiles/example_oxidation.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o -MF CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o.d -o CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o -c /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_oxidation.cpp

CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_oxidation.cpp > CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.i

CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_oxidation.cpp -o CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.s

# Object files for target example_oxidation
example_oxidation_OBJECTS = \
"CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o"

# External object files for target example_oxidation
example_oxidation_EXTERNAL_OBJECTS =

example_oxidation: CMakeFiles/example_oxidation.dir/examples/cpp/example_oxidation.cpp.o
example_oxidation: CMakeFiles/example_oxidation.dir/build.make
example_oxidation: CMakeFiles/example_oxidation.dir/compiler_depend.ts
example_oxidation: libsimulator_lib.a
example_oxidation: CMakeFiles/example_oxidation.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable example_oxidation"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/example_oxidation.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/example_oxidation.dir/build: example_oxidation
.PHONY : CMakeFiles/example_oxidation.dir/build

CMakeFiles/example_oxidation.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/example_oxidation.dir/cmake_clean.cmake
.PHONY : CMakeFiles/example_oxidation.dir/clean

CMakeFiles/example_oxidation.dir/depend:
	cd /home/<USER>/Documents/dev/projects/SemiPRO/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/dev/projects/SemiPRO /home/<USER>/Documents/dev/projects/SemiPRO /home/<USER>/Documents/dev/projects/SemiPRO/build /home/<USER>/Documents/dev/projects/SemiPRO/build /home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles/example_oxidation.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/example_oxidation.dir/depend


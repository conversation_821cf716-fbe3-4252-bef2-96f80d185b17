# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/dev/projects/SemiPRO

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/dev/projects/SemiPRO/build

# Include any dependencies generated for this target.
include CMakeFiles/example_geometry.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/example_geometry.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/example_geometry.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/example_geometry.dir/flags.make

CMakeFiles/example_geometry.dir/codegen:
.PHONY : CMakeFiles/example_geometry.dir/codegen

CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o: CMakeFiles/example_geometry.dir/flags.make
CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o: /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_geometry.cpp
CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o: CMakeFiles/example_geometry.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o -MF CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o.d -o CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o -c /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_geometry.cpp

CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_geometry.cpp > CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.i

CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/dev/projects/SemiPRO/examples/cpp/example_geometry.cpp -o CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.s

# Object files for target example_geometry
example_geometry_OBJECTS = \
"CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o"

# External object files for target example_geometry
example_geometry_EXTERNAL_OBJECTS =

example_geometry: CMakeFiles/example_geometry.dir/examples/cpp/example_geometry.cpp.o
example_geometry: CMakeFiles/example_geometry.dir/build.make
example_geometry: CMakeFiles/example_geometry.dir/compiler_depend.ts
example_geometry: libsimulator_lib.a
example_geometry: CMakeFiles/example_geometry.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable example_geometry"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/example_geometry.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/example_geometry.dir/build: example_geometry
.PHONY : CMakeFiles/example_geometry.dir/build

CMakeFiles/example_geometry.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/example_geometry.dir/cmake_clean.cmake
.PHONY : CMakeFiles/example_geometry.dir/clean

CMakeFiles/example_geometry.dir/depend:
	cd /home/<USER>/Documents/dev/projects/SemiPRO/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/dev/projects/SemiPRO /home/<USER>/Documents/dev/projects/SemiPRO /home/<USER>/Documents/dev/projects/SemiPRO/build /home/<USER>/Documents/dev/projects/SemiPRO/build /home/<USER>/Documents/dev/projects/SemiPRO/build/CMakeFiles/example_geometry.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/example_geometry.dir/depend

